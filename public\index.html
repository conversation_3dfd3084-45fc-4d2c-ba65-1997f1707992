<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulador de Testes de Transformadores</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Seu CSS Customizado -->
    <link rel="stylesheet" href="assets/custom.css">
</head>
<body>
    <div class="container-fluid p-0 d-flex flex-column" style="background-color: var(--background-main); min-height: 100vh;">
        <!-- Navbar Superior (Cabeçalho) -->
        <nav class="navbar navbar-dark mb-1" style="background-color: var(--primary-color); padding: 0.3rem 1rem; box-shadow: 0 2px 4px rgba(0,0,0,0.2); border-bottom: 1px solid var(--border-color);">
            <div class="container-fluid">
                <a class="navbar-brand d-flex align-items-center" href="#" id="home-link" style="text-decoration: none;">
                    <img src="assets/DataLogo.jpg" alt="Logo" height="40px" style="filter: drop-shadow(0px 2px 2px rgba(0,0,0,0.3)); margin-right: 15px; vertical-align: middle; border-radius: 4px;">
                    <div style="display: inline-block; vertical-align: middle;">
                        <h4 class="m-0" style="font-size: 1.1rem; letter-spacing: 0.05rem; text-shadow: 0px 1px 2px rgba(0,0,0,0.3); line-height: 1.2; color: var(--text-light);">Simulador de Testes de Transformadores</h4>
                        <div style="font-size: 0.7rem; color: rgba(255,255,255,0.85); letter-spacing: 0.03rem;">
                            <span style="font-weight: bold;">IEC/IEEE/ABNT</span> | <span style="color: var(--accent-color);">EPS 1500</span>
                        </div>
                    </div>
                </a>

                <!-- Abas dos módulos dentro da nav -->
                <div class="sidebar-container d-flex">
                    <nav class="nav d-flex">
                        <a class="nav-link d-flex align-items-center py-2 px-3 active" href="#" data-module="transformer_inputs" style="border-right: 1px solid rgba(255,255,255,0.2); color: var(--text-light); font-size: 0.8rem;">
                            <i class="fas fa-cogs me-2"></i> Dados Básicos
                        </a>
                        <a class="nav-link d-flex align-items-center py-2 px-3" href="#" data-module="losses" style="border-right: 1px solid rgba(255,255,255,0.2); color: var(--text-light); font-size: 0.8rem;">
                            <i class="fas fa-bolt me-2"></i> Perdas
                        </a>
                        <a class="nav-link d-flex align-items-center py-2 px-3" href="#" data-module="impulse" style="border-right: 1px solid rgba(255,255,255,0.2); color: var(--text-light); font-size: 0.8rem;">
                            <i class="fas fa-wave-square me-2"></i> Impulso
                        </a>
                        <a class="nav-link d-flex align-items-center py-2 px-3" href="#" data-module="dielectric_analysis" style="border-right: 1px solid rgba(255,255,255,0.2); color: var(--text-light); font-size: 0.8rem;">
                            <i class="fas fa-microscope me-2"></i> Análise Dielétrica
                        </a>
                        <a class="nav-link d-flex align-items-center py-2 px-3" href="#" data-module="applied_voltage" style="border-right: 1px solid rgba(255,255,255,0.2); color: var(--text-light); font-size: 0.8rem;">
                            <i class="fas fa-plug me-2"></i> Tensão Aplicada
                        </a>
                        <a class="nav-link d-flex align-items-center py-2 px-3" href="#" data-module="induced_voltage" style="border-right: 1px solid rgba(255,255,255,0.2); color: var(--text-light); font-size: 0.8rem;">
                            <i class="fas fa-charging-station me-2"></i> Tensão Induzida
                        </a>
                        <a class="nav-link d-flex align-items-center py-2 px-3" href="#" data-module="short_circuit" style="border-right: 1px solid rgba(255,255,255,0.2); color: var(--text-light); font-size: 0.8rem;">
                            <i class="fas fa-exclamation-triangle me-2"></i> Curto-Circuito
                        </a>
                        <a class="nav-link d-flex align-items-center py-2 px-3" href="#" data-module="temperature_rise" style="border-right: 1px solid rgba(255,255,255,0.2); color: var(--text-light); font-size: 0.8rem;">
                            <i class="fas fa-temperature-high me-2"></i> Elevação de Temperatura
                        </a>
                        <a class="nav-link d-flex align-items-center py-2 px-3" href="#" data-module="history" style="border-right: 1px solid rgba(255,255,255,0.2); color: var(--text-light); font-size: 0.8rem;">
                            <i class="fas fa-history me-2"></i> Histórico
                        </a>
                        <a class="nav-link d-flex align-items-center py-2 px-3" href="#" data-module="standards" style="color: var(--text-light); font-size: 0.8rem;">
                            <i class="fas fa-book me-2"></i> Normas
                        </a>
                    </nav>
                </div>
                <div class="d-flex align-items-center">
                    <div id="usage-counter-display" class="me-3 py-1 px-2" style="background-color: rgba(13, 202, 240, 0.2); color: var(--text-light); border: 1px solid rgba(13, 202, 240, 0.3); box-shadow: 0 1px 3px rgba(0,0,0,0.2); border-radius: 4px; display: flex; align-items: center; height: 28px;">
                        <i class="fas fa-chart-line me-1" style="font-size: 0.7rem;"></i>
                        <span style="font-size: 0.7rem; opacity: 0.9;">Uso:</span>
                        <span id="usage-value" style="font-size: 0.8rem; font-weight: bold; margin-left: 2px;">0</span>
                        <div style="width: 50px; height: 3px; background-color: rgba(255,255,255,0.2); border-radius: 2px; margin-left: 5px; display: inline-block; vertical-align: middle;">
                            <div id="usage-bar" style="width: 0%; height: 100%; background-color: var(--accent-color); border-radius: 2px; transition: width 0.5s ease-in-out;"></div>
                        </div>
                    </div>
                    <div id="limit-alert-div" class="me-3 d-none">
                        <div class="alert alert-danger d-flex align-items-center p-2 mb-0" style="font-size: 0.75rem; background-color: rgba(220, 53, 69, 0.9); color: var(--text-light); box-shadow: 0 1px 3px rgba(0,0,0,0.2); border-radius: 4px; border: 1px solid rgba(220, 53, 69, 0.5); height: 28px;">
                            <i class="fas fa-exclamation-triangle me-1"></i> Limite Atingido!
                        </div>
                    </div>
                    <button id="generate-report-btn" class="btn btn-primary btn-sm me-2" style="box-shadow: 0 1px 3px rgba(0,0,0,0.2); transition: all 0.2s ease-in-out; border: 1px solid rgba(255,255,255,0.1); height: 28px; font-size: 0.75rem; font-weight: 500; letter-spacing: 0.02rem;">
                        <i class="fas fa-file-pdf me-1"></i> Gerar Relatório PDF
                    </button>
                    <button id="global-clear-button" class="btn btn-secondary btn-sm me-2" style="background-color: var(--secondary-color); color: var(--text-header); border-radius: 3px; padding: 0.3rem 0.6rem; box-shadow: 0 1px 3px rgba(0,0,0,0.2); transition: all 0.2s ease-in-out; border: 1px solid rgba(255,255,255,0.1); height: 28px; font-size: 0.75rem; font-weight: 500; letter-spacing: 0.02rem;">
                        <i class="fas fa-eraser me-1"></i> Limpar Campos
                    </button>
                    <button id="theme-toggle" class="btn btn-outline-light btn-sm" style="background-color: rgba(255, 255, 255, 0.15); color: var(--text-light); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 4px; padding: 4px 8px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s ease-in-out; display: flex; align-items: center; height: 28px;">
                        <i id="theme-toggle-icon" class="fas fa-sun me-1" style="font-size: 0.8rem;"></i>
                        <span id="theme-toggle-text" style="font-size: 0.75rem;">Tema Claro</span>
                    </button>
                </div>
            </div>
        </nav>

        <!-- Conteúdo Principal: Área de Módulos -->
        <div class="flex-grow-1" style="min-height: calc(100vh - 120px);">
            <!-- Área de Conteúdo Principal (Módulos Injetados Aqui) -->
            <div class="container-fluid p-3" id="main-content-area" style="background-color: var(--background-main);">
                <div class="alert alert-info" style="background-color: var(--background-card); color: var(--text-light); border-left: 4px solid var(--info-color);">
                    <i class="fas fa-info-circle me-2"></i> Selecione um módulo nas abas acima para começar.
                </div>
                <!-- O conteúdo HTML dos módulos será carregado aqui dinamicamente pelo main.js -->
            </div>
        </div>

        <!-- Área para controle de log movida para baixo -->
        <div class="container-fluid p-2" style="background-color: var(--background-card-header); border-top: 1px solid var(--border-color);">
            <div id="logging-control-area" class="d-flex align-items-center justify-content-center">
                <label for="log-level-select" class="form-label me-2 mb-0" style="font-size: 0.8rem; color: var(--text-light);">Console Log Nível:</label>
                <select id="log-level-select" class="form-select form-select-sm dark-dropdown me-2" style="width: 100px;">
                    <option value="DEBUG">DEBUG</option>
                    <option value="INFO" selected>INFO</option>
                    <option value="WARN">WARN</option>
                    <option value="ERROR">ERROR</option>
                    <option value="OFF">OFF</option>
                </select>
                <button id="clear-console-btn" class="btn btn-sm btn-secondary" style="font-size: 0.7rem;">Limpar Console</button>
            </div>
        </div>

        <!-- Rodapé (Footer) -->
        <footer class="mt-auto py-1" style="background-color: var(--background-card-header); border-top: 1px solid var(--border-color); box-shadow: 0 -1px 3px rgba(0,0,0,0.1);">
            <div class="container-fluid text-center">
                <span class="text-muted" style="font-size: 0.7rem;">Transformer Test Simulator </span>
                <span class="text-muted" style="font-size: 0.7rem; opacity: 0.7;">v1.0</span>
            </div>
        </footer>
    </div>

    <!-- Modals Globais -->
    <div class="modal fade" id="clearConfirmModal" tabindex="-1" aria-labelledby="clearConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content" style="background-color: var(--background-card); color: var(--text-light);">
                <div class="modal-header" style="background-color: var(--background-card-header); color: var(--text-header);">
                    <h5 class="modal-title" id="clearConfirmModalLabel">Escolha o Tipo de Limpeza</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="filter: invert(1);"></button>
                </div>
                <div class="modal-body">
                    <p style="color: var(--text-light); margin-bottom: 20px;">Escolha o escopo da limpeza que deseja realizar:</p>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="clearScopeRadio" id="clearCurrentModule" value="current_module" checked style="background-color: var(--background-input); border-color: var(--border-color);">
                        <label class="form-check-label" for="clearCurrentModule" style="color: var(--text-light);">
                            <strong>Limpar apenas o módulo atual</strong><br>
                            <small style="color: var(--text-muted);">Remove apenas os dados do módulo que você está visualizando</small>
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="clearScopeRadio" id="clearAllModules" value="all_modules" style="background-color: var(--background-input); border-color: var(--border-color);">
                        <label class="form-check-label" for="clearAllModules" style="color: var(--text-light);">
                            <strong>Limpar todos os módulos</strong><br>
                            <small style="color: var(--text-muted);">Remove todos os dados de todos os módulos da aplicação</small>
                        </label>
                    </div>
                    <hr style="border-color: var(--border-color);">
                    <p class="fw-bold" style="color: var(--warning); font-size: 0.9rem; margin-bottom: 0;">⚠️ Esta ação não pode ser desfeita.</p>
                </div>
                <div class="modal-footer" style="background-color: var(--background-card); border-top: 1px solid var(--border-color);">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="clear-cancel-button">Cancelar</button>
                    <button type="button" class="btn btn-danger" id="clear-confirm-button">Confirmar Limpeza</button>
                </div>
            </div>
        </div>
    </div>


    <!-- Bootstrap JS Bundle (JQuery não é mais necessário com Bootstrap 5) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Script principal para gerenciar o carregamento das páginas dos módulos -->

    <script src="scripts/main.js" type="module"></script>
</body>
</html>