// public/scripts/main.js - Versão Atualizada para Roteamento SPA

document.addEventListener('DOMContentLoaded', () => {
    // --- Existing Functionality (Preserved and Integrated) ---
    // Theme Toggle
    const themeToggle = document.getElementById('theme-toggle');
    const themeToggleIcon = document.getElementById('theme-toggle-icon');
    const themeToggleText = document.getElementById('theme-toggle-text');
    const body = document.body;
    
    // Function to apply the selected theme
    function applyTheme(theme) {
        body.setAttribute('data-bs-theme', theme);
        if (theme === 'dark') {
            if (themeToggleIcon) themeToggleIcon.classList.replace('fa-moon', 'fa-sun');
            if (themeToggleText) themeToggleText.textContent = 'Tema Claro';
        } else {
            if (themeToggleIcon) themeToggleIcon.classList.replace('fa-sun', 'fa-moon');
            if (themeToggleText) themeToggleText.textContent = 'Tema Escuro';
        }
        localStorage.setItem('theme', theme);
    }

    // Initialize theme based on localStorage or default to dark
    const currentTheme = localStorage.getItem('theme') || 'dark';
    applyTheme(currentTheme);

    if (themeToggle) {
        themeToggle.addEventListener('click', () => {
            let newTheme = body.getAttribute('data-bs-theme') === 'dark' ? 'light' : 'dark';
            applyTheme(newTheme);
        });
    }

    // Modal for Clearing Fields
    const globalClearButton = document.getElementById('global-clear-button');
    const clearConfirmModalElement = document.getElementById('clearConfirmModal');
    let clearConfirmModalInstance;
    if (clearConfirmModalElement) {
        clearConfirmModalInstance = new bootstrap.Modal(clearConfirmModalElement);
    }
    const clearConfirmButton = document.getElementById('clear-confirm-button');

    if (globalClearButton && clearConfirmModalInstance) {
        globalClearButton.addEventListener('click', () => {
            clearConfirmModalInstance.show();
        });
    }

    if (clearConfirmButton && clearConfirmModalInstance) {
        clearConfirmButton.addEventListener('click', () => {
            const scopeElement = document.querySelector('input[name="clearScopeRadio"]:checked');
            if (scopeElement) {
                const scope = scopeElement.value;
                console.log(`Confirmado: Limpar ${scope}`);
                
                if (scope === 'current_module') {
                    const currentModuleHash = window.location.hash.substring(1);
                    const moduleToClear = currentModuleHash || 'transformer_inputs';
                    const event = new CustomEvent('clearModuleData', { detail: { module: moduleToClear } });
                    document.dispatchEvent(event);
                    console.log(`Event clearModuleData dispatched for module: ${moduleToClear}`);
                } else if (scope === 'all_modules') {
                    const event = new CustomEvent('clearAllModulesData');
                    document.dispatchEvent(event);
                    console.log("Event clearAllModulesData dispatched.");
                }
            }
            clearConfirmModalInstance.hide();
        });
    }
    
    // Usage Counter Simulation
    const usageValueElement = document.getElementById('usage-value');
    const usageBarElement = document.getElementById('usage-bar');
    const limitAlertDiv = document.getElementById('limit-alert-div');
    const MAX_USAGE = 1000; // Example limit
    let currentUsage = 0; // This should ideally be loaded/managed from a persistent state or backend

    function updateUsageDisplay() {
        if (usageValueElement) usageValueElement.textContent = currentUsage;
        if (usageBarElement) {
            const percentage = (currentUsage / MAX_USAGE) * 100;
            usageBarElement.style.width = `${Math.min(percentage, 100)}%`;
        }
        if (limitAlertDiv) {
            if (currentUsage >= MAX_USAGE) {
                limitAlertDiv.classList.remove('d-none');
            } else {
                limitAlertDiv.classList.add('d-none');
            }
        }
    }
    // Call initially to set up the display
    updateUsageDisplay();
    // Example of how usage might be incremented by other parts of the application:
    // document.addEventListener('simulationUsed', (event) => { 
    //     currentUsage += event.detail.amount || 10; 
    //     updateUsageDisplay(); 
    // });


    // --- SPA Routing Logic ---
    const mainContentArea = document.getElementById('main-content-area');
    const navLinks = document.querySelectorAll('.sidebar-container .nav-link[data-module]');
    const homeLink = document.getElementById('home-link'); // For logo/home button
    let currentModuleScriptTag = null; 

    function setActiveSidebarLink(moduleName) {
        navLinks.forEach(link => {
            if (link.dataset.module === moduleName) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }

    async function loadModulePage(moduleName, pushToHistory = true) {
        if (!moduleName) {
            console.warn('loadModulePage called with no moduleName. Defaulting to transformer_inputs.');
            moduleName = 'transformer_inputs';
        }
        
        const htmlFilePath = `pages/${moduleName}.html`;
        const scriptFilePath = `./scripts/${moduleName}.js`; // Use ./ for relative path in import()

        // Show a loading indicator
        if (mainContentArea) {
            mainContentArea.innerHTML = `
                <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="ms-3 mb-0">Carregando módulo ${moduleName}...</p>
                </div>`;
        }

        try {
            const response = await fetch(htmlFilePath);
            if (!response.ok) {
                throw new Error(`HTTP error ${response.status} while fetching ${htmlFilePath}`);
            }
            const htmlContent = await response.text();
            if (mainContentArea) mainContentArea.innerHTML = htmlContent;

            // Remove o script do módulo anterior, se houver
            // Nota: Para módulos ES6 carregados dinamicamente, não há uma tag <script> anexada ao DOM
            // da mesma forma que scripts tradicionais. currentModuleScriptTag aqui é mais um placeholder
            // para indicar que um módulo foi carregado e pode ter recursos a serem limpos.
            if (currentModuleScriptTag) {
                // Se o módulo anterior foi carregado via import dinâmico, não há um elemento DOM para remover
                // A limpeza de recursos específicos do módulo deve ser feita dentro do próprio módulo
                currentModuleScriptTag = null; 
            }

            try {
                // Carrega o script do módulo dinamicamente como um módulo ES6
                // O import() retorna uma Promise que resolve para o objeto módulo
                const module = await import(scriptFilePath);
                console.log(`[main.js] Módulo ${moduleName}.js carregado dinamicamente.`, module);

                // Dispara um evento customizado para o módulo saber que seu conteúdo HTML foi carregado
                // e que ele pode inicializar suas funcionalidades.
                // O transformer_inputs.js já escuta por 'moduleContentLoaded' e chama initTransformerInputs()
                document.dispatchEvent(new CustomEvent('moduleContentLoaded', { detail: { moduleName } }));

            } catch (scriptLoadError) {
                // Se o script não existe ou há um erro de carregamento (ex: SyntaxError)
                if (scriptLoadError instanceof TypeError && scriptLoadError.message.includes('Failed to fetch dynamically imported module')) {
                    console.log(`[main.js] Nenhum script de módulo encontrado ou erro de carregamento para ${moduleName} em ${scriptFilePath}. Isso pode ser normal para módulos sem JS.`, scriptLoadError);
                } else {
                    console.error(`[main.js] Erro ao carregar o script do módulo ${moduleName} em ${scriptFilePath}:`, scriptLoadError);
                }
            }
            
            setActiveSidebarLink(moduleName);

            if (pushToHistory) {
                history.pushState({ module: moduleName }, `${moduleName.replace('_', ' ')} - Simulador`, `#${moduleName}`);
            } else {
                // Ensure title is updated even if not pushing history (e.g. on popstate or initial load)
                document.title = `${moduleName.replace('_', ' ')} - Simulador`;
            }
            
            // O evento 'moduleContentLoaded' já foi disparado após o carregamento do script.
            // Removido o disparo duplicado aqui.

        } catch (error) {
            console.error(`Error loading module ${moduleName}:`, error);
            if (mainContentArea) {
                mainContentArea.innerHTML = `<div class="alert alert-danger m-3">Erro ao carregar o módulo: ${moduleName}. Verifique o console.</div>`;
            }
            setActiveSidebarLink(null);
        }
    }

    navLinks.forEach(link => {
        link.addEventListener('click', (event) => {
            event.preventDefault();
            const moduleName = link.dataset.module;
            if (moduleName) {
                // Avoid reloading if already on the same module, unless it's a forced reload
                const currentHashModule = window.location.hash.substring(1);
                if (moduleName !== currentHashModule) {
                    loadModulePage(moduleName);
                } else {
                    // If clicking the active link again, perhaps a soft reload or do nothing
                    // For now, do nothing to prevent unnecessary reloads.
                    // console.log("Module already active:", moduleName);
                }
            }
        });
    });
    
    if (homeLink) {
        homeLink.addEventListener('click', (event) => {
            event.preventDefault();
            loadModulePage('transformer_inputs', true);
        });
    }

    window.addEventListener('popstate', (event) => {
        let moduleToLoad = 'transformer_inputs'; 
        if (event.state && event.state.module) {
            moduleToLoad = event.state.module;
        } else {
            const hash = window.location.hash.substring(1);
            if (hash) {
                const isValidModule = Array.from(navLinks).some(navLink => navLink.dataset.module === hash);
                if (isValidModule) {
                    moduleToLoad = hash;
                } else {
                    console.warn(`Invalid module in hash on popstate: ${hash}. Loading default.`);
                }
            }
        }
        loadModulePage(moduleToLoad, false); 
    });

    function initializeAppRouting() {
        const initialHash = window.location.hash.substring(1);
        let moduleToLoadOnStart = 'transformer_inputs'; 

        if (initialHash) {
            const isValidModule = Array.from(navLinks).some(navLink => navLink.dataset.module === initialHash);
            if (isValidModule) {
                moduleToLoadOnStart = initialHash;
                loadModulePage(moduleToLoadOnStart, false); 
            } else {
                console.warn(`Invalid module in initial URL hash: '${initialHash}'. Loading default module.`);
                loadModulePage(moduleToLoadOnStart, true); 
            }
        } else {
            loadModulePage(moduleToLoadOnStart, true);
        }
    }

    initializeAppRouting();
});